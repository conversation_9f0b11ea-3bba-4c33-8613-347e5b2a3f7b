-- Job-Posted Webhook Flow Test Data Setup
-- This script sets up comprehensive test data for QA/QC testing

-- ============================================
-- 1. CLEANUP EXISTING TEST DATA
-- ============================================

-- Remove existing test data
DELETE FROM job_notification_recipients WHERE job_notification_campaign_id IN (
    SELECT id FROM job_notification_campaigns WHERE job_id LIKE 'test_%' OR job_id LIKE 'qa_%'
);

DELETE FROM job_notification_campaigns WHERE job_id LIKE 'test_%' OR job_id LIKE 'qa_%';

DELETE FROM scraping_jobs WHERE location_query IN ('12345', '35210', '90210', '99999');

DELETE FROM businesses WHERE name LIKE '%Test%' OR email LIKE '%test.com' OR email LIKE '%qa.com';

DELETE FROM zip_codes WHERE zip_code IN ('12345', '12346', '12347', '35210', '35211', '90210', '99999');

-- ============================================
-- 2. ZIP CODES FOR RADIUS TESTING
-- ============================================

INSERT INTO zip_codes (zip_code, lat, lng, created_at, updated_at) VALUES
-- Birmingham, AL area (for main testing)
('35210', 33.5186, -86.8104, NOW(), NOW()),  -- Center point
('35211', 33.5150, -86.8090, NOW(), NOW()),  -- ~3 miles away
('35212', 33.5220, -86.8130, NOW(), NOW()),  -- ~5 miles away
('35213', 33.5300, -86.8200, NOW(), NOW()),  -- ~10 miles away
('35214', 33.5500, -86.8400, NOW(), NOW()),  -- ~25 miles away

-- New York area (for secondary testing)
('12345', 40.7128, -74.0060, NOW(), NOW()),  -- Manhattan center
('12346', 40.7200, -74.0100, NOW(), NOW()),  -- ~5 miles away
('12347', 40.7500, -74.0200, NOW(), NOW()),  -- ~25 miles away
('12348', 40.8000, -74.0500, NOW(), NOW()),  -- ~50 miles away

-- Beverly Hills, CA (for no businesses testing)
('90210', 34.0901, -118.4065, NOW(), NOW()),

-- Non-existent zip code for edge case testing
('99999', 0.0000, 0.0000, NOW(), NOW());

-- ============================================
-- 3. COMPLETE BUSINESSES (READY FOR NOTIFICATIONS)
-- ============================================

INSERT INTO businesses (
    name, email, phone, address, zip_code, lat, lng, category, 
    website, about, business_hours, status, created_at, updated_at
) VALUES
-- Birmingham area - Complete businesses
('Birmingham Elite Cleaning', '<EMAIL>', '************', 
 '100 Elite Way, Birmingham, AL 35210', '35210', 33.5186, -86.8104, 'cleaning',
 'https://elite-cleaning.com', 'Professional cleaning services since 2010',
 '{"monday": "8:00-17:00", "tuesday": "8:00-17:00", "wednesday": "8:00-17:00", "thursday": "8:00-17:00", "friday": "8:00-17:00"}',
 'active', NOW(), NOW()),

('Sparkle Pro Services', '<EMAIL>', '************',
 '200 Sparkle Ave, Birmingham, AL 35210', '35210', 33.5200, -86.8120, 'cleaning',
 'https://sparkle-pro.com', 'Eco-friendly cleaning solutions',
 '{"monday": "9:00-18:00", "tuesday": "9:00-18:00", "wednesday": "9:00-18:00", "thursday": "9:00-18:00", "friday": "9:00-18:00", "saturday": "9:00-15:00"}',
 'active', NOW(), NOW()),

('Clean Masters LLC', '<EMAIL>', '************',
 '300 Master St, Birmingham, AL 35211', '35211', 33.5150, -86.8090, 'cleaning',
 'https://clean-masters.com', 'Commercial and residential cleaning experts',
 '{"monday": "7:00-19:00", "tuesday": "7:00-19:00", "wednesday": "7:00-19:00", "thursday": "7:00-19:00", "friday": "7:00-19:00", "saturday": "8:00-16:00"}',
 'active', NOW(), NOW()),

-- Nearby zip codes
('Pristine Cleaners', '<EMAIL>', '205-555-0104',
 '400 Pristine Blvd, Birmingham, AL 35212', '35212', 33.5220, -86.8130, 'cleaning',
 'https://pristine-cleaners.com', 'Luxury home cleaning services',
 '{"monday": "8:00-17:00", "tuesday": "8:00-17:00", "wednesday": "8:00-17:00", "thursday": "8:00-17:00", "friday": "8:00-17:00"}',
 'active', NOW(), NOW()),

('Quick Clean Co', '<EMAIL>', '205-555-0105',
 '500 Quick Lane, Birmingham, AL 35213', '35213', 33.5300, -86.8200, 'cleaning',
 'https://quick-clean.com', 'Fast and reliable cleaning services',
 '{"monday": "6:00-20:00", "tuesday": "6:00-20:00", "wednesday": "6:00-20:00", "thursday": "6:00-20:00", "friday": "6:00-20:00", "saturday": "8:00-18:00", "sunday": "10:00-16:00"}',
 'active', NOW(), NOW());

-- ============================================
-- 4. INCOMPLETE BUSINESSES (FOR ENHANCEMENT TESTING)
-- ============================================

-- Missing email (should trigger enhancement)
INSERT INTO businesses (
    name, phone, address, zip_code, lat, lng, category, 
    website, status, created_at, updated_at
) VALUES
('No Email Cleaners', '************', 
 '600 No Email St, Birmingham, AL 35210', '35210', 33.5180, -86.8100, 'cleaning',
 'https://no-email.com', 'active', NOW(), NOW()),

('Missing Contact Info', '************',
 '700 Missing Ave, Birmingham, AL 35211', '35211', 33.5140, -86.8080, 'cleaning',
 NULL, 'active', NOW(), NOW());

-- Missing coordinates (should trigger enhancement)
INSERT INTO businesses (
    name, email, phone, address, zip_code, category, 
    website, status, created_at, updated_at
) VALUES
('No GPS Cleaning', '<EMAIL>', '************',
 '800 No GPS Blvd, Birmingham, AL 35210', '35210', 'cleaning',
 'https://no-gps.com', 'active', NOW(), NOW()),

('Coordinate Missing Co', '<EMAIL>', '************',
 '900 Coords St, Birmingham, AL 35211', '35211', 'cleaning',
 'https://coords-missing.com', 'active', NOW(), NOW());

-- Missing address details
INSERT INTO businesses (
    name, email, phone, address, zip_code, lat, lng, category, 
    status, created_at, updated_at
) VALUES
('Partial Address Clean', '<EMAIL>', '************',
 'Birmingham, AL', '35210', 33.5170, -86.8090, 'cleaning',
 'active', NOW(), NOW());

-- ============================================
-- 5. BUSINESSES IN DIFFERENT CATEGORIES
-- ============================================

INSERT INTO businesses (
    name, email, phone, address, zip_code, lat, lng, category, 
    website, status, created_at, updated_at
) VALUES
-- Plumbing services
('Birmingham Plumbing Pro', '<EMAIL>', '************',
 '1000 Plumber Way, Birmingham, AL 35210', '35210', 33.5190, -86.8110, 'plumbing',
 'https://plumbing-pro.com', 'active', NOW(), NOW()),

-- Electrical services  
('Elite Electrical', '<EMAIL>', '************',
 '1100 Electric Ave, Birmingham, AL 35210', '35210', 33.5195, -86.8115, 'electrical',
 'https://elite-electrical.com', 'active', NOW(), NOW()),

-- Landscaping services
('Green Thumb Landscaping', '<EMAIL>', '************',
 '1200 Garden St, Birmingham, AL 35211', '35211', 33.5160, -86.8095, 'landscaping',
 'https://green-thumb.com', 'active', NOW(), NOW());

-- ============================================
-- 6. BUSINESSES AT DIFFERENT DISTANCES
-- ============================================

-- Very close (within 5 miles)
INSERT INTO businesses (
    name, email, phone, address, zip_code, lat, lng, category, 
    website, status, created_at, updated_at
) VALUES
('Close Range Cleaning', '<EMAIL>', '************',
 '1300 Close St, Birmingham, AL 35210', '35210', 33.5200, -86.8120, 'cleaning',
 'https://close-range.com', 'active', NOW(), NOW());

-- Medium distance (15-20 miles)
INSERT INTO businesses (
    name, email, phone, address, zip_code, lat, lng, category, 
    website, status, created_at, updated_at
) VALUES
('Medium Range Clean', '<EMAIL>', '************',
 '1400 Medium Ave, Birmingham, AL 35213', '35213', 33.5300, -86.8200, 'cleaning',
 'https://medium-range.com', 'active', NOW(), NOW());

-- Far distance (25+ miles)
INSERT INTO businesses (
    name, email, phone, address, zip_code, lat, lng, category, 
    website, status, created_at, updated_at
) VALUES
('Far Range Cleaners', '<EMAIL>', '************',
 '1500 Far Blvd, Birmingham, AL 35214', '35214', 33.5500, -86.8400, 'cleaning',
 'https://far-range.com', 'active', NOW(), NOW());

-- ============================================
-- 7. INACTIVE/SUSPENDED BUSINESSES
-- ============================================

INSERT INTO businesses (
    name, email, phone, address, zip_code, lat, lng, category, 
    website, status, created_at, updated_at
) VALUES
('Inactive Cleaning Co', '<EMAIL>', '************',
 '1600 Inactive St, Birmingham, AL 35210', '35210', 33.5185, -86.8105, 'cleaning',
 'https://inactive.com', 'inactive', NOW(), NOW()),

('Suspended Services', '<EMAIL>', '************',
 '1700 Suspended Ave, Birmingham, AL 35210', '35210', 33.5188, -86.8108, 'cleaning',
 'https://suspended.com', 'suspended', NOW(), NOW());

-- ============================================
-- 8. NEW YORK AREA BUSINESSES (FOR SECONDARY TESTING)
-- ============================================

INSERT INTO businesses (
    name, email, phone, address, zip_code, lat, lng, category, 
    website, status, created_at, updated_at
) VALUES
('NYC Elite Cleaning', '<EMAIL>', '************',
 '100 Manhattan St, New York, NY 12345', '12345', 40.7128, -74.0060, 'cleaning',
 'https://nyc-elite.com', 'active', NOW(), NOW()),

('Brooklyn Clean Pro', '<EMAIL>', '************',
 '200 Brooklyn Ave, New York, NY 12346', '12346', 40.7200, -74.0100, 'cleaning',
 'https://brooklyn-clean.com', 'active', NOW(), NOW());

-- ============================================
-- 9. VERIFICATION QUERIES
-- ============================================

-- Count businesses by zip code and status
SELECT 
    zip_code,
    status,
    category,
    COUNT(*) as business_count,
    SUM(CASE WHEN email IS NOT NULL THEN 1 ELSE 0 END) as with_email,
    SUM(CASE WHEN lat IS NOT NULL AND lng IS NOT NULL THEN 1 ELSE 0 END) as with_coordinates
FROM businesses 
WHERE zip_code IN ('35210', '35211', '35212', '35213', '35214', '12345', '12346', '90210')
GROUP BY zip_code, status, category
ORDER BY zip_code, status, category;

-- Count zip codes
SELECT COUNT(*) as zip_code_count FROM zip_codes 
WHERE zip_code IN ('35210', '35211', '35212', '35213', '35214', '12345', '12346', '12347', '12348', '90210', '99999');

-- Show distance calculation example
SELECT 
    name,
    zip_code,
    ROUND((3959 * acos(
        cos(radians(33.5186)) *
        cos(radians(lat)) *
        cos(radians(lng) - radians(-86.8104)) +
        sin(radians(33.5186)) *
        sin(radians(lat))
    )), 2) AS distance_miles
FROM businesses
WHERE zip_code IN ('35210', '35211', '35212', '35213', '35214')
    AND lat IS NOT NULL 
    AND lng IS NOT NULL
ORDER BY distance_miles;

-- ============================================
-- 10. TEST DATA SUMMARY
-- ============================================

SELECT 'TEST DATA SETUP COMPLETED' as status;
SELECT 
    'Complete businesses ready for notifications' as description,
    COUNT(*) as count 
FROM businesses 
WHERE email IS NOT NULL 
    AND lat IS NOT NULL 
    AND lng IS NOT NULL 
    AND status = 'active'
    AND zip_code IN ('35210', '35211', '35212', '35213', '35214');

SELECT 
    'Incomplete businesses for enhancement testing' as description,
    COUNT(*) as count 
FROM businesses 
WHERE (email IS NULL OR lat IS NULL OR lng IS NULL)
    AND zip_code IN ('35210', '35211', '35212', '35213', '35214');

SELECT 
    'Zip codes for radius testing' as description,
    COUNT(*) as count 
FROM zip_codes 
WHERE zip_code IN ('35210', '35211', '35212', '35213', '35214', '12345', '12346', '12347', '12348', '90210', '99999');
