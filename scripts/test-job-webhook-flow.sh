#!/bin/bash

# Job-Posted Webhook Flow Testing Script
# This script automates the testing of the complete job-posted webhook flow

set -e  # Exit on any error

# Configuration
WEBHOOK_URL="http://localhost:8000/api/webhooks/job-posted"
WEBHOOK_TOKEN="test_webhook_token_12345"
DB_NAME="jobon_test"
MYSQL_USER="root"
MYSQL_PASS=""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}✅ $1${NC}"
}

warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if curl is available
    if ! command -v curl &> /dev/null; then
        error "curl is required but not installed"
        exit 1
    fi
    
    # Check if mysql is available
    if ! command -v mysql &> /dev/null; then
        error "mysql client is required but not installed"
        exit 1
    fi
    
    # Check if PHP artisan is available
    if [ ! -f "artisan" ]; then
        error "This script must be run from the Laravel project root directory"
        exit 1
    fi
    
    success "Prerequisites check passed"
}

# Setup test environment
setup_test_environment() {
    log "Setting up test environment..."
    
    # Clear previous test data
    mysql -u$MYSQL_USER -p$MYSQL_PASS $DB_NAME << EOF
DELETE FROM job_notification_recipients WHERE job_notification_campaign_id IN (
    SELECT id FROM job_notification_campaigns WHERE job_id LIKE 'test_%'
);
DELETE FROM job_notification_campaigns WHERE job_id LIKE 'test_%';
DELETE FROM scraping_jobs WHERE location_query IN ('12345', '35210');
DELETE FROM jobs WHERE payload LIKE '%test_%';
DELETE FROM failed_jobs WHERE payload LIKE '%test_%';
EOF
    
    # Insert test businesses
    mysql -u$MYSQL_USER -p$MYSQL_PASS $DB_NAME << EOF
INSERT IGNORE INTO businesses (name, email, phone, address, zip_code, lat, lng, category, created_at, updated_at) VALUES
('Test Clean Pro', '<EMAIL>', '555-0101', '100 Test St, Test City, ST 12345', '12345', 40.7128, -74.0060, 'cleaning', NOW(), NOW()),
('Test Sparkle Clean', '<EMAIL>', '555-0102', '200 Test Ave, Test City, ST 12345', '12345', 40.7130, -74.0062, 'cleaning', NOW(), NOW()),
('Test Elite Cleaners', '<EMAIL>', '555-0103', '300 Test Blvd, Test City, ST 12345', '12345', 40.7132, -74.0064, 'cleaning', NOW(), NOW());
EOF

    # Insert test zip codes
    mysql -u$MYSQL_USER -p$MYSQL_PASS $DB_NAME << EOF
INSERT IGNORE INTO zip_codes (zip_code, lat, lng) VALUES
('12345', 40.7128, -74.0060),
('12346', 40.7200, -74.0100),
('35210', 33.5186, -86.8104),
('35211', 33.5150, -86.8090);
EOF
    
    success "Test environment setup completed"
}

# Test webhook endpoint
test_webhook_endpoint() {
    log "Testing webhook endpoint..."
    
    local test_payload='{
        "event_type": "job_posted",
        "event_id": "test_webhook_001",
        "timestamp": "'$(date '+%Y-%m-%d %H:%M:%S')'",
        "job_data": {
            "job_id": "test_webhook_001",
            "title": "Test Webhook Cleaning",
            "description": "Testing webhook endpoint",
            "category_id": 15,
            "zip_code": "12345",
            "address": "123 Webhook Test St",
            "latitude": 40.7128,
            "longitude": -74.0060,
            "budget_min": 100,
            "budget_max": 200,
            "currency": "USD",
            "customer_email": "<EMAIL>",
            "customer_name": "Webhook Test",
            "created_at": "'$(date '+%Y-%m-%d %H:%M:%S')'"
        }
    }'
    
    local response=$(curl -s -w "%{http_code}" -X POST "$WEBHOOK_URL" \
        -H "Content-Type: application/json" \
        -H "X-Webhook-Token: $WEBHOOK_TOKEN" \
        -d "$test_payload")
    
    local http_code="${response: -3}"
    local response_body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        success "Webhook endpoint test passed (HTTP $http_code)"
        log "Response: $response_body"
    else
        error "Webhook endpoint test failed (HTTP $http_code)"
        log "Response: $response_body"
        return 1
    fi
}

# Test authentication failure
test_webhook_auth_failure() {
    log "Testing webhook authentication failure..."
    
    local test_payload='{"event_type": "job_posted", "event_id": "test_auth_fail"}'
    
    local response=$(curl -s -w "%{http_code}" -X POST "$WEBHOOK_URL" \
        -H "Content-Type: application/json" \
        -H "X-Webhook-Token: wrong_token" \
        -d "$test_payload")
    
    local http_code="${response: -3}"
    
    if [ "$http_code" = "401" ] || [ "$http_code" = "403" ]; then
        success "Authentication failure test passed (HTTP $http_code)"
    else
        error "Authentication failure test failed - expected 401/403, got $http_code"
        return 1
    fi
}

# Process queue jobs
process_queue_jobs() {
    log "Processing queue jobs..."
    
    # Process default queue
    timeout 30s php artisan queue:work --once --queue=default || true
    
    # Check if any jobs failed
    local failed_count=$(mysql -u$MYSQL_USER -p$MYSQL_PASS $DB_NAME -se "SELECT COUNT(*) FROM failed_jobs WHERE payload LIKE '%test_%'")
    
    if [ "$failed_count" -gt 0 ]; then
        warning "Found $failed_count failed jobs"
        mysql -u$MYSQL_USER -p$MYSQL_PASS $DB_NAME -e "SELECT id, queue, exception FROM failed_jobs WHERE payload LIKE '%test_%' ORDER BY failed_at DESC LIMIT 3"
    else
        success "No failed jobs found"
    fi
}

# Test business discovery
test_business_discovery() {
    log "Testing business discovery..."
    
    # Check if campaign was created
    local campaign_count=$(mysql -u$MYSQL_USER -p$MYSQL_PASS $DB_NAME -se "SELECT COUNT(*) FROM job_notification_campaigns WHERE job_id = 'test_webhook_001'")
    
    if [ "$campaign_count" -eq 1 ]; then
        success "Campaign created successfully"
        
        # Get campaign details
        mysql -u$MYSQL_USER -p$MYSQL_PASS $DB_NAME -e "SELECT id, job_id, status, business_count FROM job_notification_campaigns WHERE job_id = 'test_webhook_001'"
        
        # Check recipients
        local recipient_count=$(mysql -u$MYSQL_USER -p$MYSQL_PASS $DB_NAME -se "
            SELECT COUNT(*) FROM job_notification_recipients jnr 
            JOIN job_notification_campaigns jnc ON jnr.job_notification_campaign_id = jnc.id 
            WHERE jnc.job_id = 'test_webhook_001'
        ")
        
        if [ "$recipient_count" -gt 0 ]; then
            success "Found $recipient_count recipients"
            mysql -u$MYSQL_USER -p$MYSQL_PASS $DB_NAME -e "
                SELECT jnr.business_name, jnr.business_email, jnr.status 
                FROM job_notification_recipients jnr 
                JOIN job_notification_campaigns jnc ON jnr.job_notification_campaign_id = jnc.id 
                WHERE jnc.job_id = 'test_webhook_001'
            "
        else
            warning "No recipients found"
        fi
    else
        error "Campaign not created or multiple campaigns found (count: $campaign_count)"
        return 1
    fi
}

# Test no businesses scenario
test_no_businesses_scenario() {
    log "Testing no businesses scenario..."
    
    local test_payload='{
        "event_type": "job_posted",
        "event_id": "test_no_biz_001",
        "timestamp": "'$(date '+%Y-%m-%d %H:%M:%S')'",
        "job_data": {
            "job_id": "test_no_biz_001",
            "title": "Test No Businesses",
            "description": "Testing no businesses scenario",
            "category_id": 15,
            "zip_code": "99999",
            "address": "999 No Business St",
            "customer_email": "<EMAIL>",
            "customer_name": "No Biz Test",
            "created_at": "'$(date '+%Y-%m-%d %H:%M:%S')'"
        }
    }'
    
    # Send webhook
    curl -s -X POST "$WEBHOOK_URL" \
        -H "Content-Type: application/json" \
        -H "X-Webhook-Token: $WEBHOOK_TOKEN" \
        -d "$test_payload" > /dev/null
    
    # Process job
    timeout 30s php artisan queue:work --once --queue=default || true
    
    # Check if scraping job was created
    local scraping_count=$(mysql -u$MYSQL_USER -p$MYSQL_PASS $DB_NAME -se "
        SELECT COUNT(*) FROM scraping_jobs sj 
        JOIN job_notification_campaigns jnc ON sj.job_notification_campaign_id = jnc.id 
        WHERE jnc.job_id = 'test_no_biz_001'
    ")
    
    if [ "$scraping_count" -gt 0 ]; then
        success "Scraping job created for no businesses scenario"
        mysql -u$MYSQL_USER -p$MYSQL_PASS $DB_NAME -e "
            SELECT sj.id, sj.status, sj.location_query, sj.category_query 
            FROM scraping_jobs sj 
            JOIN job_notification_campaigns jnc ON sj.job_notification_campaign_id = jnc.id 
            WHERE jnc.job_id = 'test_no_biz_001'
        "
    else
        warning "No scraping job created"
    fi
}

# Generate test report
generate_report() {
    log "Generating test report..."
    
    echo ""
    echo "=========================================="
    echo "         TEST EXECUTION REPORT"
    echo "=========================================="
    echo ""
    
    # Campaign summary
    echo "📊 Campaign Summary:"
    mysql -u$MYSQL_USER -p$MYSQL_PASS $DB_NAME -e "
        SELECT 
            status,
            COUNT(*) as count,
            GROUP_CONCAT(job_id) as job_ids
        FROM job_notification_campaigns 
        WHERE job_id LIKE 'test_%' 
        GROUP BY status
    "
    echo ""
    
    # Recipient summary
    echo "📧 Recipient Summary:"
    mysql -u$MYSQL_USER -p$MYSQL_PASS $DB_NAME -e "
        SELECT 
            jnr.status,
            COUNT(*) as count
        FROM job_notification_recipients jnr 
        JOIN job_notification_campaigns jnc ON jnr.job_notification_campaign_id = jnc.id 
        WHERE jnc.job_id LIKE 'test_%' 
        GROUP BY jnr.status
    "
    echo ""
    
    # Queue job summary
    echo "⚙️ Queue Job Summary:"
    local total_jobs=$(mysql -u$MYSQL_USER -p$MYSQL_PASS $DB_NAME -se "SELECT COUNT(*) FROM jobs WHERE payload LIKE '%test_%'")
    local failed_jobs=$(mysql -u$MYSQL_USER -p$MYSQL_PASS $DB_NAME -se "SELECT COUNT(*) FROM failed_jobs WHERE payload LIKE '%test_%'")
    echo "Total jobs processed: $total_jobs"
    echo "Failed jobs: $failed_jobs"
    echo ""
    
    # Scraping job summary
    echo "🔍 Scraping Job Summary:"
    mysql -u$MYSQL_USER -p$MYSQL_PASS $DB_NAME -e "
        SELECT 
            sj.status,
            COUNT(*) as count
        FROM scraping_jobs sj 
        JOIN job_notification_campaigns jnc ON sj.job_notification_campaign_id = jnc.id 
        WHERE jnc.job_id LIKE 'test_%' 
        GROUP BY sj.status
    "
    echo ""
    
    success "Test execution completed!"
}

# Cleanup test data
cleanup() {
    log "Cleaning up test data..."
    
    mysql -u$MYSQL_USER -p$MYSQL_PASS $DB_NAME << EOF
DELETE FROM job_notification_recipients WHERE job_notification_campaign_id IN (
    SELECT id FROM job_notification_campaigns WHERE job_id LIKE 'test_%'
);
DELETE FROM job_notification_campaigns WHERE job_id LIKE 'test_%';
DELETE FROM scraping_jobs WHERE location_query IN ('99999');
DELETE FROM jobs WHERE payload LIKE '%test_%';
DELETE FROM failed_jobs WHERE payload LIKE '%test_%';
DELETE FROM businesses WHERE email LIKE '%@test.com';
EOF
    
    success "Cleanup completed"
}

# Main execution
main() {
    echo "🚀 Starting Job-Posted Webhook Flow Testing"
    echo "==========================================="
    echo ""
    
    check_prerequisites
    setup_test_environment
    
    echo ""
    echo "🧪 Running Test Cases..."
    echo "------------------------"
    
    # Test 1: Basic webhook functionality
    test_webhook_endpoint
    process_queue_jobs
    test_business_discovery
    
    echo ""
    
    # Test 2: Authentication failure
    test_webhook_auth_failure
    
    echo ""
    
    # Test 3: No businesses scenario
    test_no_businesses_scenario
    
    echo ""
    
    # Generate report
    generate_report
    
    # Ask if user wants to cleanup
    echo ""
    read -p "Do you want to cleanup test data? (y/N): " -n 1 -r
    echo ""
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        cleanup
    else
        warning "Test data preserved for manual inspection"
    fi
}

# Handle script interruption
trap 'echo ""; error "Script interrupted"; exit 1' INT

# Run main function
main "$@"
